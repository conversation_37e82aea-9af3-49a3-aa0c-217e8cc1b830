import json
import os
from typing import Dict, List, Optional

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            # 创建默认配置
            self.config = {
                "telegram": {
                    "api_id": "",
                    "api_hash": "",
                    "phone": ""
                },
                "cloud115": {
                    "cookie": ""
                },
                "default_cid": "",
                "channels": []
            }
            self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    # Telegram配置
    def get_telegram_config(self) -> dict:
        """获取Telegram配置"""
        return self.config.get("telegram", {})
    
    def set_telegram_config(self, api_id: str, api_hash: str, phone: str):
        """设置Telegram配置"""
        self.config["telegram"] = {
            "api_id": api_id,
            "api_hash": api_hash,
            "phone": phone
        }
        self.save_config()
    
    # 115网盘配置
    def get_cloud115_config(self) -> dict:
        """获取115网盘配置"""
        return self.config.get("cloud115", {})
    
    def set_cloud115_cookie(self, cookie: str):
        """设置115网盘Cookie"""
        self.config["cloud115"]["cookie"] = cookie
        self.save_config()
    
    # 默认CID
    def get_default_cid(self) -> str:
        """获取默认CID"""
        return self.config.get("default_cid", "")
    
    def set_default_cid(self, cid: str):
        """设置默认CID"""
        self.config["default_cid"] = cid
        self.save_config()
    
    # 频道配置
    def get_channels(self) -> List[str]:
        """获取监听频道列表"""
        return self.config.get("channels", [])
    
    def add_channel(self, channel: str):
        """添加监听频道"""
        if "channels" not in self.config:
            self.config["channels"] = []
        
        if channel not in self.config["channels"]:
            self.config["channels"].append(channel)
            self.save_config()
    
    def remove_channel(self, channel: str):
        """移除监听频道"""
        if "channels" in self.config and channel in self.config["channels"]:
            self.config["channels"].remove(channel)
            self.save_config()