import re
import requests
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class Cloud115:
    """115网盘操作类"""
    
    def __init__(self, cookie: str):
        self.session = requests.Session()
        self.session.cookies.update({
            'UID': cookie
        })
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def extract_share_info(self, text: str) -> Optional[Tuple[str, Optional[str]]]:
        """
        从文本中提取分享链接和提取码
        
        Args:
            text: 包含分享链接的文本
            
        Returns:
            (链接, 提取码)的元组，如果未找到则返回None
        """
        # 匹配115分享链接
        link_pattern = r'https?://115\.com/s/[a-zA-Z0-9]+'
        link_match = re.search(link_pattern, text)
        
        if not link_match:
            return None
            
        link = link_match.group(0)
        
        # 尝试提取提取码（通常在链接后面）
        code_pattern = r'[a-zA-Z0-9]{4}'
        # 查找链接后的4位字符
        code_search_text = text[link_match.end():link_match.end() + 10]
        code_match = re.search(code_pattern, code_search_text)
        code = code_match.group(0) if code_match else None
        
        return (link, code)
    
    def save_to_cid(self, share_link: str, cid: str, code: Optional[str] = None) -> bool:
        """
        将分享链接保存到指定CID目录
        
        Args:
            share_link: 分享链接
            cid: 目标目录CID
            code: 提取码（如果需要）
            
        Returns:
            保存是否成功
        """
        try:
            # 从分享链接提取关键信息
            share_key = share_link.split('/')[-1]
            
            # 访问分享链接获取信息
            receive_url = f"https://webapi.115.com/share/sheet?share_code={share_key}"
            if code:
                receive_url += f"&receive_code={code}"
            
            response = self.session.get(receive_url)
            if response.status_code != 200:
                logger.error(f"获取分享信息失败: {response.status_code}")
                return False
            
            # 解析响应获取文件信息
            data = response.json()
            if data.get("state") != True:
                logger.error(f"分享链接无效: {data.get('msg', '未知错误')}")
                return False
            
            # 执行转存操作
            # 注意：这是一个简化的实现，实际的API调用可能更复杂
            save_data = {
                "cid": cid,
                "share_code": share_key
            }
            
            if code:
                save_data["receive_code"] = code
            
            save_response = self.session.post(
                "https://webapi.115.com/share/receive",
                data=save_data
            )
            
            if save_response.status_code == 200:
                save_result = save_response.json()
                if save_result.get("state") == True:
                    logger.info(f"成功转存到CID {cid}")
                    return True
                else:
                    logger.error(f"转存失败: {save_result.get('msg', '未知错误')}")
            else:
                logger.error(f"转存请求失败: {save_response.status_code}")
                
        except Exception as e:
            logger.error(f"转存过程中发生错误: {e}")
            
        return False