import asyncio
import logging
from bot.telegram_bot import TelegramBot

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def main():
    """主函数，启动机器人"""
    logger.info("正在启动网盘助手机器人...")
    
    try:
        # 初始化并运行机器人
        bot = TelegramBot()
        await bot.start()
    except Exception as e:
        logger.error(f"启动机器人时出错: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())