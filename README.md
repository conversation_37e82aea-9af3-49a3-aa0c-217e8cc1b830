# 网盘助手

一个自动将Telegram频道中的115网盘分享链接转存到指定目录的工具。

## 功能特点

- 自动监听Telegram频道消息
- 识别并提取115网盘分享链接
- 自动将资源转存到指定目录
- 支持提取码自动识别

## 安装与配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 获取Telegram API凭证

1. 访问 https://my.telegram.org/
2. 登录并创建应用
3. 获取 `api_id` 和 `api_hash`

### 3. 配置参数

首次运行时需要配置以下参数：

1. Telegram配置:
   - `api_id`: Telegram应用ID
   - `api_hash`: Telegram应用哈希
   - `phone`: 你的手机号码

2. 115网盘配置:
   - `cookie`: 115网盘的Cookie信息

3. 其他配置:
   - `default_cid`: 默认转存目录的CID
   - `channels`: 要监听的频道列表

## 使用方法

### 启动机器人

```bash
python src/main.py
```

### 配置参数

在Telegram中向机器人发送以下命令进行配置：

1. 设置Telegram参数:
   ```
   /set_telegram <api_id> <api_hash> <phone>
   ```

2. 设置115 Cookie:
   ```
   /set_cookie <cookie>
   ```

3. 设置默认CID:
   ```
   /set_cid <cid>
   ```

4. 添加监听频道:
   ```
   /listen <频道用户名或ID>
   ```

## 工作原理

1. 机器人连接到Telegram账户
2. 监听配置的频道消息
3. 从消息中提取115分享链接和提取码
4. 调用115 API将资源保存到指定目录
5. 记录操作结果

## 注意事项

- 确保Telegram账户已加入要监听的频道
- 115 Cookie需要保持有效
- 频道用户名需要以@开头或使用频道ID