from telethon import Telegram<PERSON>lient, connection
from telethon.tl.types import DataJSON

# MTProto 配置
TEST_DC_IP = '**************'  # 测试服务器IP
TEST_DC_PORT = 443             # 端口
PUBLIC_KEY = '''-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyMEdY1aR+sCR3ZSJrtztKTKqigvO/vBfqACJLZtS7QMgCGXJ6XIR
yy7mx66W0/sOFa7/1mAZtEoIokDP3ShoqF4fVNb6XeqgQfaUHd8wJpDWHcR2OFwv
plUUI1PLTktZ9uW2WE23b+ixNwJjJGwBDJPQEQFBE+vfmH0JP503wr5INS1poWg/
j25sIWeYPHYeOrFp/eXaqhISP6G+q2IeTaWTXpwZj4LzXq5YOpk4bYEQ6mvRq7D1
aHWfYmlEGepfaYR8Q0YqvvhYtMte3ITnuSJs171+GDqpdKcSwHnd6FudwGO4pcCO
j4WcDuXc2CTHgH8gFTNhp/Y8/SpDOhvn9QIDAQAB
-----END RSA PUBLIC KEY-----'''

# 你的 API 信息（从 my.telegram.org 获取）
API_ID = 123456  # 替换为你的API_ID
API_HASH = 'your_api_hash_here'  # 替换为你的API_HASH

async def main():
    # 创建客户端（使用MTProxy连接）
    client = TelegramClient(
        'session_name',
        API_ID,
        API_HASH,
        connection=connection.ConnectionTcpMTProxyRandomizedIntermediate,
        proxy=(TEST_DC_IP, TEST_DC_PORT, PUBLIC_KEY.strip())
    )

    # 连接并打印账号信息
    await client.start()
    me = await client.get_me()
    print(f"成功登录账号: {me.username} (ID: {me.id})")

    # 示例：获取最近5条消息
    async for message in client.iter_messages('me', limit=5):
        print(message.text)

    await client.disconnect()

# 运行
import asyncio
asyncio.run(main())