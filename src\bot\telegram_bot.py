import asyncio
import logging
from telethon import TelegramClient, events
from config.config import Config
from utils.cloud115 import Cloud115

logger = logging.getLogger(__name__)

class TelegramBot:
    """Telegram机器人主类"""
    
    def __init__(self):
        self.config = Config()
        self.cloud115 = None
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化Telegram客户端"""
        telegram_config = self.config.get_telegram_config()
        api_id = telegram_config.get("api_id")
        api_hash = telegram_config.get("api_hash")
        phone = telegram_config.get("phone")
        
        if not all([api_id, api_hash, phone]):
            logger.warning("Telegram配置不完整，请先配置Telegram参数")
            return
            
        self.client = TelegramClient('session_name', api_id, api_hash)
    
    async def start(self):
        """启动机器人"""
        if not self.client:
            logger.error("Telegram客户端未初始化，请检查配置")
            return
            
        # 初始化115网盘客户端
        cloud115_config = self.config.get_cloud115_config()
        cookie = cloud115_config.get("cookie")
        if cookie:
            self.cloud115 = Cloud115(cookie)
        
        # 注册事件处理器
        self._register_handlers()
        
        # 启动客户端
        await self.client.start()
        
        # 开始监听频道
        await self._start_channel_listening()
        
        # 保持运行
        logger.info("机器人已启动，正在监听消息...")
        await self.client.run_until_disconnected()
    
    def _register_handlers(self):
        """注册事件处理器"""
        if not self.client:
            return
            
        @self.client.on(events.NewMessage(pattern=r'/start'))
        async def start_handler(event):
            await event.reply("欢迎使用网盘助手！\n\n"
                            "使用方法：\n"
                            "1. 发送 /config 配置参数\n"
                            "2. 发送 /listen 添加监听频道\n"
                            "3. 机器人会自动处理频道中的分享链接")
        
        @self.client.on(events.NewMessage(pattern=r'/config'))
        async def config_handler(event):
            await event.reply("请按以下格式发送配置信息：\n\n"
                            "设置Telegram配置：\n"
                            "/set_telegram <api_id> <api_hash> <phone>\n\n"
                            "设置115 Cookie：\n"
                            "/set_cookie <cookie>\n\n"
                            "设置默认CID：\n"
                            "/set_cid <cid>")
        
        @self.client.on(events.NewMessage(pattern=r'/set_telegram (.+) (.+) (.+)'))
        async def set_telegram_handler(event):
            api_id, api_hash, phone = event.pattern_match.group(1), \
                                     event.pattern_match.group(2), \
                                     event.pattern_match.group(3)
            
            self.config.set_telegram_config(api_id, api_hash, phone)
            await event.reply("Telegram配置已保存")
        
        @self.client.on(events.NewMessage(pattern=r'/set_cookie (.+)'))
        async def set_cookie_handler(event):
            cookie = event.pattern_match.group(1)
            self.config.set_cloud115_cookie(cookie)
            
            # 更新115客户端
            self.cloud115 = Cloud115(cookie)
            await event.reply("115 Cookie已保存")
        
        @self.client.on(events.NewMessage(pattern=r'/set_cid (.+)'))
        async def set_cid_handler(event):
            cid = event.pattern_match.group(1)
            self.config.set_default_cid(cid)
            await event.reply(f"默认CID已设置为: {cid}")
        
        @self.client.on(events.NewMessage(pattern=r'/listen (.+)'))
        async def listen_handler(event):
            channel = event.pattern_match.group(1)
            self.config.add_channel(channel)
            await event.reply(f"已添加监听频道: {channel}")
        
        @self.client.on(events.NewMessage(incoming=True))
        async def message_handler(event):
            # 处理普通消息中的分享链接
            if self.cloud115 and hasattr(event.message, 'text') and event.message.text:
                await self._process_message(event.message.text, "手动消息")
    
    async def _start_channel_listening(self):
        """开始监听频道"""
        channels = self.config.get_channels()
        if not channels:
            logger.info("未配置监听频道")
            return
            
        logger.info(f"开始监听 {len(channels)} 个频道: {channels}")
    
    async def _process_message(self, text: str, source: str):
        """处理包含分享链接的消息"""
        if not self.cloud115:
            logger.warning("115客户端未初始化")
            return
            
        # 提取分享信息
        share_info = self.cloud115.extract_share_info(text)
        if not share_info:
            return
            
        link, code = share_info
        cid = self.config.get_default_cid()
        
        if not cid:
            logger.warning("未设置默认CID")
            return
            
        logger.info(f"检测到分享链接: {link} 来自 {source}")
        
        # 执行转存
        success = self.cloud115.save_to_cid(link, cid, code)
        if success:
            logger.info(f"成功转存: {link}")
        else:
            logger.error(f"转存失败: {link}")