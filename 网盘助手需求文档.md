# 网盘助手机器人需求文档

## 1. 产品概述
网盘助手是一个基于Telegram的自动化工具，帮助用户将他人分享的115网盘资源快速转存到自己的网盘中。该工具通过监听频道消息，自动识别并转存网盘资源。

## 2. 目标用户
- 经常需要转存他人分享的网盘资源的用户
- 希望简化手动转存操作的用户

## 3. 核心功能

### 3.1 基础转存功能
- 解析用户发送的115网盘分享链接
- 自动提取链接中的提取码（如果提供）
- 将资源转存到用户指定的网盘目录

### 3.2 频道监听功能
- 监听用户已加入的Telegram频道
- 自动识别频道消息中的115网盘链接
- 自动执行转存操作

### 3.3 简单配置管理
- 配置115网盘账户信息
- 设置默认转存目录
- 管理监听的频道列表

## 4. 用户交互

### 4.1 基本命令
- `/start` - 开始使用助手
- `/help` - 获取帮助信息
- `/config` - 配置基本参数
- `/cid` - 设置转存目录

### 4.2 转存流程
1. 用户通过Telegram向助手发送网盘链接
2. 助手自动解析链接和提取码
3. 助手将资源转存到预设目录
4. 助手返回转存结果给用户

### 4.3 频道自动转存流程
1. 助手监听用户配置的频道消息
2. 从消息中提取115网盘链接和提取码
3. 自动将资源转存到预设目录
4. 在日志中记录转存结果（可选推送结果到用户）

## 5. 技术实现

### 5.1 技术栈
- 后端：Python
- Telegram交互：telethon库
- 网盘操作：115 API或模拟浏览器操作

### 5.2 核心处理流程
1. 使用telethon连接Telegram账户
2. 监听指定频道的新消息
3. 消息内容解析，提取网盘链接
4. 调用115 API执行转存操作
5. 记录操作结果

### 5.3 简化配置
```
Cookie: UID=22116836_R1_1*
默认转存目录CID: ************
监听频道: @channel1;@channel2
```

### 5.4 链接解析规则
- 匹配消息中的115网盘链接（https://115.com/s/...格式）
- 自动提取链接后的提取码
- 过滤无效链接和非网盘内容

## 6. 非功能需求

### 6.1 基本要求
- 稳定运行，自动处理常见错误
- 记录操作日志便于排查问题
- 响应时间合理（<5秒）

### 6.2 安全性
- 用户敏感信息（Cookie）加密存储
- 防止未授权访问

## 7. 开发计划
1. 第一阶段：搭建基础框架，实现Telegram机器人基本功能
2. 第二阶段：集成115网盘API，实现基本转存功能
3. 第三阶段：实现频道监听和自动转存功能
4. 第四阶段：完善配置管理和错误处理

## 8. 使用说明

### 8.1 配置步骤
1. 获取Telegram API凭证（api_id和api_hash）
2. 配置115网盘Cookie信息
3. 设置默认转存目录CID
4. 配置需要监听的频道列表

### 8.2 自动转存工作原理
当助手运行时：
- 自动连接到Telegram账户
- 监听用户指定的频道
- 实时获取新消息
- 从消息中提取115链接
- 自动完成转存操作
- 记录转存结果

## 9. 成功指标
1. 转存成功率 >90%
2. 操作简便，用户上手快
3. 系统稳定，故障率低